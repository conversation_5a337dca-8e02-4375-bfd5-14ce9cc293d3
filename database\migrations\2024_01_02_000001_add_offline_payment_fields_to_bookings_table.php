<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Update payment_method enum to include offline payment options
            $table->string('payment_method')->change();
            
            // Add fields for offline payment feedback
            $table->string('customer_payment_name')->nullable()->after('payment_status');
            $table->string('customer_transaction_id')->nullable()->after('customer_payment_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Remove the new fields
            $table->dropColumn(['customer_payment_name', 'customer_transaction_id']);
            
            // Revert payment_method back to enum
            $table->enum('payment_method', ['mobile_money', 'card', 'cash'])->change();
        });
    }
};
