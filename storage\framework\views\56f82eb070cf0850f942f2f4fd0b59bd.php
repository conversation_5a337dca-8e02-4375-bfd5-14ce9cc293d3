<?php $__env->startSection('title', 'Book a Delivery - TTAJet'); ?>

<?php $__env->startPush('body-class', 'booking-page'); ?>

<?php $__env->startSection('content'); ?>

<?php
    // Get settings for the booking form
    $companySettings = \App\Models\Setting::getGroup('company');
    $currencySettings = \App\Models\Setting::getGroup('currency');
    $pricingSettings = \App\Models\Setting::getGroup('pricing');
    $paymentSettings = \App\Models\Setting::getGroup('payments');

    // Set defaults if settings don't exist
    $companyName = $companySettings['name'] ?? 'TTAJet Courier Service';
    $companyPhone = $companySettings['phone'] ?? '+233 XX XXX XXXX';
    $currencySymbol = $currencySettings['symbol'] ?? '$';
    $currencyCode = $currencySettings['code'] ?? 'USD';

    // Get offline payment methods (only show if name is not empty)
    $offlinePaymentMethods = [];
    for ($i = 1; $i <= 3; $i++) {
        $name = $paymentSettings["offline_method_{$i}_name"] ?? '';
        if (!empty($name)) {
            $offlinePaymentMethods[] = [
                'id' => "offline_method_{$i}",
                'name' => $name,
                'instructions' => $paymentSettings["offline_method_{$i}_instructions"] ?? '',
                'account' => $paymentSettings["offline_method_{$i}_account"] ?? '',
            ];
        }
    }
?>
<div class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen py-8 px-4">
    <div class="max-w-5xl mx-auto">

        <!-- Back Button -->
        <div class="mb-6">
            <a href="<?php echo e(route('home')); ?>" class="inline-flex items-center text-gray-600 hover:text-orange-500 font-medium">
                <i class="fas fa-arrow-left mr-2"></i>Back to Home
            </a>
        </div>

        <!-- Main Form Container -->
        <div class="bg-white rounded-2xl shadow-xl overflow-hidden">

            <!-- Header -->
            <div class="bg-gradient-to-r from-orange-500 to-orange-600 px-8 py-6">
                <h1 class="text-3xl md:text-4xl font-bold text-white text-center">Book a Delivery</h1>
                <p class="text-orange-100 text-center mt-2">Fast, reliable courier service with <?php echo e($companyName); ?></p>
            </div>

            <!-- Progress Bar -->
            <div class="bg-gray-50 px-8 py-4">
                <div class="flex items-center justify-between max-w-2xl mx-auto">
                    <div class="flex items-center space-x-4">
                        <div class="step-indicator active" data-step="1">
                            <div class="step-circle">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <span class="step-label">Locations</span>
                        </div>
                        <div class="step-connector"></div>
                        <div class="step-indicator" data-step="2">
                            <div class="step-circle">
                                <i class="fas fa-box"></i>
                            </div>
                            <span class="step-label">Package</span>
                        </div>
                        <div class="step-connector"></div>
                        <div class="step-indicator" data-step="3">
                            <div class="step-circle">
                                <i class="fas fa-users"></i>
                            </div>
                            <span class="step-label">Contacts</span>
                        </div>
                        <div class="step-connector"></div>
                        <div class="step-indicator" data-step="4">
                            <div class="step-circle">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <span class="step-label">Payment</span>
                        </div>
                        <div class="step-connector"></div>
                        <div class="step-indicator" data-step="5">
                            <div class="step-circle">
                                <i class="fas fa-check"></i>
                            </div>
                            <span class="step-label">Review</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Content -->
            <div class="p-8 md:p-12">
                <form id="booking-form" method="POST" action="<?php echo e(route('booking.store')); ?>" class="form-container">
                    <?php echo csrf_field(); ?>

                    <!-- Step 1: Locations -->
                    <div class="form-step active" data-step="1">
                        <div class="step-header mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-2">Where are we picking up and delivering?</h2>
                            <p class="text-gray-600">Enter the pickup and delivery locations for your package</p>
                        </div>

                        <div class="space-y-6">
                            <!-- Pickup Location -->
                            <div class="location-input-group">
                                <label class="block text-sm font-semibold text-gray-700 mb-3">
                                    <i class="fas fa-circle text-green-500 mr-2"></i>Pickup Location
                                </label>
                                <div class="relative">
                                    <i class="fas fa-map-marker-alt absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                    <input type="text" name="pickup_address" id="pickup_address"
                                           value="<?php echo e(old('pickup_address', request('pickup_address'))); ?>"
                                           placeholder="Enter pickup address or use current location"
                                           class="w-full pl-12 pr-24 py-4 rounded-xl border-2 border-gray-200 focus:border-orange-500 focus:ring-0 focus:outline-none text-lg placeholder-gray-400 <?php $__errorArgs = ['pickup_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           autocomplete="off"
                                           required>
                                    <button type="button" id="pickup-locate-btn"
                                            class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-orange-500 hover:bg-orange-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                                            title="Use current location">
                                        <i class="fas fa-location-arrow"></i>
                                    </button>
                                    <div class="pickup-suggestions absolute z-10 w-full bg-white border border-gray-200 rounded-lg mt-1 hidden shadow-lg max-h-60 overflow-y-auto"></div>
                                </div>
                                <!-- Hidden fields for coordinates -->
                                <input type="hidden" name="pickup_latitude" id="pickup_latitude" value="<?php echo e(old('pickup_latitude')); ?>">
                                <input type="hidden" name="pickup_longitude" id="pickup_longitude" value="<?php echo e(old('pickup_longitude')); ?>">
                                <input type="hidden" name="pickup_place_id" id="pickup_place_id" value="<?php echo e(old('pickup_place_id')); ?>">
                                <?php $__errorArgs = ['pickup_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-2 flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                                    </p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Delivery Location -->
                            <div class="location-input-group">
                                <label class="block text-sm font-semibold text-gray-700 mb-3">
                                    <i class="fas fa-circle text-red-500 mr-2"></i>Delivery Location
                                </label>
                                <div class="relative">
                                    <i class="fas fa-map-marker-alt absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                    <input type="text" name="delivery_address" id="delivery_address"
                                           value="<?php echo e(old('delivery_address', request('delivery_address'))); ?>"
                                           placeholder="Enter delivery address or use current location"
                                           class="w-full pl-12 pr-24 py-4 rounded-xl border-2 border-gray-200 focus:border-orange-500 focus:ring-0 focus:outline-none text-lg placeholder-gray-400 <?php $__errorArgs = ['delivery_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           autocomplete="off"
                                           required>
                                    <button type="button" id="delivery-locate-btn"
                                            class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-orange-500 hover:bg-orange-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                                            title="Use current location">
                                        <i class="fas fa-location-arrow"></i>
                                    </button>
                                    <div class="delivery-suggestions absolute z-10 w-full bg-white border border-gray-200 rounded-lg mt-1 hidden shadow-lg max-h-60 overflow-y-auto"></div>
                                </div>
                                <!-- Hidden fields for coordinates -->
                                <input type="hidden" name="delivery_latitude" id="delivery_latitude" value="<?php echo e(old('delivery_latitude')); ?>">
                                <input type="hidden" name="delivery_longitude" id="delivery_longitude" value="<?php echo e(old('delivery_longitude')); ?>">
                                <input type="hidden" name="delivery_place_id" id="delivery_place_id" value="<?php echo e(old('delivery_place_id')); ?>">
                                <?php $__errorArgs = ['delivery_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-2 flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                                    </p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Distance & Cost Preview -->
                            <div id="route-preview" class="hidden bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <div class="bg-blue-100 rounded-full p-3">
                                            <i class="fas fa-route text-blue-600"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-600">Estimated Distance</p>
                                            <p class="text-lg font-semibold text-gray-900" id="estimated-distance">-- km</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm text-gray-600">Estimated Duration</p>
                                        <p class="text-lg font-semibold text-gray-900" id="estimated-duration">-- mins</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Map Preview -->
                            <div id="map-preview" class="hidden bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="text-sm font-semibold text-gray-700">
                                        <i class="fas fa-map text-orange-500 mr-2"></i>Route Preview
                                    </h4>
                                    <button type="button" id="toggle-map" class="text-xs text-orange-600 hover:text-orange-700 font-medium">
                                        <i class="fas fa-expand-arrows-alt mr-1"></i>Expand
                                    </button>
                                </div>
                                <div id="booking-map" style="height: 200px; width: 100%;" class="rounded-lg border border-gray-200"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Package Information -->
                    <div class="form-step" data-step="2">
                        <div class="step-header mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-2">Tell us about your package</h2>
                            <p class="text-gray-600">Package details help us provide accurate pricing and proper handling</p>
                        </div>

                        <div class="space-y-8">
                            <!-- Package Type Selection -->
                            <div>
                                <label class="block text-sm font-semibold text-gray-700 mb-4">Package Type</label>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div class="package-type-option" data-type="document">
                                        <input type="radio" name="package_type" value="document" id="type-document" class="hidden" <?php echo e(old('package_type', request('package_type')) == 'document' ? 'checked' : ''); ?> required>
                                        <label for="type-document" class="package-type-card">
                                            <div class="package-icon">
                                                <i class="fas fa-file-alt"></i>
                                            </div>
                                            <h3 class="package-title">Document</h3>
                                            <p class="package-desc">Letters, papers, contracts</p>
                                            <p class="package-size">Up to 1kg</p>
                                            <p class="package-price">From <?php echo e(\App\Models\Setting::formatCurrency($pricingSettings['base_cost_document'] ?? 15.00)); ?></p>
                                        </label>
                                    </div>

                                    <div class="package-type-option" data-type="small">
                                        <input type="radio" name="package_type" value="small" id="type-small" class="hidden" <?php echo e(old('package_type', request('package_type')) == 'small' ? 'checked' : ''); ?>>
                                        <label for="type-small" class="package-type-card">
                                            <div class="package-icon">
                                                <i class="fas fa-box"></i>
                                            </div>
                                            <h3 class="package-title">Small Box</h3>
                                            <p class="package-desc">Books, electronics, gifts</p>
                                            <p class="package-size">Up to 5kg</p>
                                            <p class="package-price">From <?php echo e(\App\Models\Setting::formatCurrency($pricingSettings['base_cost_small'] ?? 20.00)); ?></p>
                                        </label>
                                    </div>

                                    <div class="package-type-option" data-type="medium">
                                        <input type="radio" name="package_type" value="medium" id="type-medium" class="hidden" <?php echo e(old('package_type', request('package_type')) == 'medium' ? 'checked' : ''); ?>>
                                        <label for="type-medium" class="package-type-card">
                                            <div class="package-icon">
                                                <i class="fas fa-boxes"></i>
                                            </div>
                                            <h3 class="package-title">Medium Box</h3>
                                            <p class="package-desc">Clothing, shoes, appliances</p>
                                            <p class="package-size">Up to 15kg</p>
                                            <p class="package-price">From <?php echo e(\App\Models\Setting::formatCurrency($pricingSettings['base_cost_medium'] ?? 35.00)); ?></p>
                                        </label>
                                    </div>

                                    <div class="package-type-option" data-type="large">
                                        <input type="radio" name="package_type" value="large" id="type-large" class="hidden" <?php echo e(old('package_type', request('package_type')) == 'large' ? 'selected' : ''); ?>>
                                        <label for="type-large" class="package-type-card">
                                            <div class="package-icon">
                                                <i class="fas fa-cube"></i>
                                            </div>
                                            <h3 class="package-title">Large Box</h3>
                                            <p class="package-desc">Furniture, large items</p>
                                            <p class="package-size">Up to 50kg</p>
                                            <p class="package-price">From <?php echo e(\App\Models\Setting::formatCurrency($pricingSettings['base_cost_large'] ?? 50.00)); ?></p>
                                        </label>
                                    </div>
                                </div>
                                <?php $__errorArgs = ['package_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-2 flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                                    </p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Package Weight and Pickup Time -->
                            <div class="grid md:grid-cols-2 gap-6">
                                <!-- Package Weight -->
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-3">Package Weight (kg)</label>
                                    <div class="relative">
                                        <i class="fas fa-weight-hanging absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                        <input type="number" name="package_weight" id="package_weight"
                                               value="<?php echo e(old('package_weight')); ?>"
                                               placeholder="Enter weight" step="0.1" min="0" max="100"
                                               class="w-full pl-12 pr-16 py-4 rounded-xl border-2 border-gray-200 focus:border-orange-500 focus:ring-0 focus:outline-none text-lg placeholder-gray-400 <?php $__errorArgs = ['package_weight'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               required>
                                        <span class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">kg</span>
                                    </div>
                                    <?php $__errorArgs = ['package_weight'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-sm mt-2 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                                        </p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Pickup Time Preference -->
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-3">When should we pick up?</label>
                                    <div class="relative">
                                        <i class="fas fa-clock absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                        <select name="pickup_time_preference"
                                                class="w-full pl-12 pr-10 py-4 rounded-xl border-2 border-gray-200 focus:border-orange-500 focus:ring-0 focus:outline-none text-lg appearance-none bg-white <?php $__errorArgs = ['pickup_time_preference'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                required>
                                            <option value="now" <?php echo e(old('pickup_time_preference') == 'now' ? 'selected' : ''); ?>>As soon as possible</option>
                                            <option value="1_hour" <?php echo e(old('pickup_time_preference') == '1_hour' ? 'selected' : ''); ?>>In 1 hour</option>
                                            <option value="2_hours" <?php echo e(old('pickup_time_preference') == '2_hours' ? 'selected' : ''); ?>>In 2 hours</option>
                                            <option value="scheduled" <?php echo e(old('pickup_time_preference') == 'scheduled' ? 'selected' : ''); ?>>Schedule for later</option>
                                        </select>
                                        <i class="fas fa-chevron-down absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                    </div>
                                    <?php $__errorArgs = ['pickup_time_preference'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-sm mt-2 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                                        </p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Package Description -->
                            <div>
                                <label class="block text-sm font-semibold text-gray-700 mb-3">Package Description (Optional)</label>
                                <textarea name="package_description" rows="3"
                                          placeholder="Briefly describe your package contents (e.g., fragile items, electronics, documents)"
                                          class="w-full px-4 py-4 rounded-xl border-2 border-gray-200 focus:border-orange-500 focus:ring-0 focus:outline-none resize-none placeholder-gray-400 bg-white <?php $__errorArgs = ['package_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"><?php echo e(old('package_description')); ?></textarea>
                                <?php $__errorArgs = ['package_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-2 flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                                    </p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-step" data-step="3">
                        <div class="step-header mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-2">Who's involved in this delivery?</h2>
                            <p class="text-gray-600">Contact information for pickup and delivery coordination</p>
                        </div>

                        <div class="space-y-8">
                            <!-- Pickup Contact Section -->
                            <div class="contact-section">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-green-600 text-sm"></i>
                                    </div>
                                    Pickup Contact
                                </h3>
                                <div class="grid md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-semibold text-gray-700 mb-3">Full Name</label>
                                        <div class="relative">
                                            <i class="fas fa-user absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                            <input type="text" name="pickup_person_name"
                                                   value="<?php echo e(old('pickup_person_name', auth()->user()->name)); ?>"
                                                   placeholder="Enter full name"
                                                   class="w-full pl-12 pr-4 py-4 rounded-xl border-2 border-gray-200 focus:border-orange-500 focus:ring-0 focus:outline-none text-lg placeholder-gray-400 bg-white <?php $__errorArgs = ['pickup_person_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   required>
                                        </div>
                                        <?php $__errorArgs = ['pickup_person_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 text-sm mt-2 flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                                            </p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-semibold text-gray-700 mb-3">Phone Number</label>
                                        <div class="relative">
                                            <i class="fas fa-phone absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                            <input type="tel" name="pickup_person_phone"
                                                   value="<?php echo e(old('pickup_person_phone', auth()->user()->phone_number)); ?>"
                                                   placeholder="<?php echo e($companyPhone); ?>"
                                                   class="w-full pl-12 pr-4 py-4 rounded-xl border-2 border-gray-200 focus:border-orange-500 focus:ring-0 focus:outline-none text-lg placeholder-gray-400 bg-white <?php $__errorArgs = ['pickup_person_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   required>
                                        </div>
                                        <?php $__errorArgs = ['pickup_person_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 text-sm mt-2 flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                                            </p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Delivery Contact Section -->
                            <div class="contact-section">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-user-check text-red-600 text-sm"></i>
                                    </div>
                                    Delivery Contact (Receiver)
                                </h3>
                                <div class="grid md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-semibold text-gray-700 mb-3">Full Name</label>
                                        <div class="relative">
                                            <i class="fas fa-user-check absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                            <input type="text" name="receiver_name"
                                                   value="<?php echo e(old('receiver_name')); ?>"
                                                   placeholder="Enter receiver's full name"
                                                   class="w-full pl-12 pr-4 py-4 rounded-xl border-2 border-gray-200 focus:border-orange-500 focus:ring-0 focus:outline-none text-lg placeholder-gray-400 bg-white <?php $__errorArgs = ['receiver_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   required>
                                        </div>
                                        <?php $__errorArgs = ['receiver_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 text-sm mt-2 flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                                            </p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-semibold text-gray-700 mb-3">Phone Number</label>
                                        <div class="relative">
                                            <i class="fas fa-phone-alt absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                            <input type="tel" name="receiver_phone"
                                                   value="<?php echo e(old('receiver_phone')); ?>"
                                                   placeholder="<?php echo e($companyPhone); ?>"
                                                   class="w-full pl-12 pr-4 py-4 rounded-xl border-2 border-gray-200 focus:border-orange-500 focus:ring-0 focus:outline-none text-lg placeholder-gray-400 bg-white <?php $__errorArgs = ['receiver_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   required>
                                        </div>
                                        <?php $__errorArgs = ['receiver_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 text-sm mt-2 flex items-center">
                                                <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                                            </p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Special Instructions -->
                            <div>
                                <label class="block text-sm font-semibold text-gray-700 mb-3">Special Instructions (Optional)</label>
                                <textarea name="special_instructions" rows="3"
                                          placeholder="Any special delivery instructions, access codes, or notes for our courier"
                                          class="w-full px-4 py-4 rounded-xl border-2 border-gray-200 focus:border-orange-500 focus:ring-0 focus:outline-none resize-none placeholder-gray-400 bg-white <?php $__errorArgs = ['special_instructions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"><?php echo e(old('special_instructions')); ?></textarea>
                                <?php $__errorArgs = ['special_instructions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-2 flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                                    </p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Step 4: Payment Method -->
                    <div class="form-step" data-step="4">
                        <div class="step-header mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-2">How would you like to pay?</h2>
                            <p class="text-gray-600">Choose your preferred payment method for this delivery</p>
                        </div>

                        <div class="space-y-6">
                            <!-- Payment Methods -->
                            <div class="grid md:grid-cols-<?php echo e(count($offlinePaymentMethods) + 1 > 3 ? '2' : (count($offlinePaymentMethods) + 1)); ?> gap-4">
                                <!-- Cash on Delivery -->
                                <div class="payment-method-option">
                                    <input type="radio" name="payment_method" value="cash" id="payment-cash" class="hidden" <?php echo e(old('payment_method', 'cash') == 'cash' ? 'checked' : ''); ?> required>
                                    <label for="payment-cash" class="payment-method-card">
                                        <div class="payment-icon">
                                            <i class="fas fa-money-bill-wave"></i>
                                        </div>
                                        <h3 class="payment-title">Cash on Delivery</h3>
                                        <p class="payment-desc">Pay when delivered</p>
                                        <div class="payment-badge">Popular</div>
                                    </label>
                                </div>

                                <!-- Offline Payment Methods -->
                                <?php $__currentLoopData = $offlinePaymentMethods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="payment-method-option">
                                    <input type="radio" name="payment_method" value="<?php echo e($method['id']); ?>" id="payment-<?php echo e($method['id']); ?>" class="hidden" <?php echo e(old('payment_method') == $method['id'] ? 'checked' : ''); ?>>
                                    <label for="payment-<?php echo e($method['id']); ?>" class="payment-method-card">
                                        <div class="payment-icon">
                                            <i class="fas fa-university"></i>
                                        </div>
                                        <h3 class="payment-title"><?php echo e($method['name']); ?></h3>
                                        <p class="payment-desc">Offline Payment</p>
                                    </label>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-2 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                                </p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                            <!-- Offline Payment Details -->
                            <?php $__currentLoopData = $offlinePaymentMethods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div id="offline-details-<?php echo e($method['id']); ?>" class="offline-payment-details hidden bg-blue-50 rounded-xl p-6 border border-blue-200">
                                <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                    <?php echo e($method['name']); ?> - Payment Instructions
                                </h4>

                                <div class="space-y-4">
                                    <!-- Instructions -->
                                    <div class="bg-white rounded-lg p-4 border border-blue-200">
                                        <h5 class="font-medium text-gray-900 mb-2">Instructions:</h5>
                                        <p class="text-gray-700"><?php echo e($method['instructions']); ?></p>
                                    </div>

                                    <!-- Account Details -->
                                    <div class="bg-white rounded-lg p-4 border border-blue-200">
                                        <h5 class="font-medium text-gray-900 mb-2">Account Details:</h5>
                                        <p class="text-gray-700 font-mono"><?php echo e($method['account']); ?></p>
                                    </div>

                                    <!-- Payment Feedback Form -->
                                    <div class="bg-white rounded-lg p-4 border border-blue-200">
                                        <h5 class="font-medium text-gray-900 mb-4">Payment Confirmation</h5>
                                        <div class="grid md:grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Your Payment Name</label>
                                                <input type="text"
                                                       name="customer_payment_name"
                                                       value="<?php echo e(old('customer_payment_name')); ?>"
                                                       placeholder="Name used for payment"
                                                       class="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-0 focus:outline-none <?php $__errorArgs = ['customer_payment_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                                <?php $__errorArgs = ['customer_payment_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Transaction ID</label>
                                                <input type="text"
                                                       name="customer_transaction_id"
                                                       value="<?php echo e(old('customer_transaction_id')); ?>"
                                                       placeholder="Transaction reference number"
                                                       class="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-0 focus:outline-none <?php $__errorArgs = ['customer_transaction_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                                <?php $__errorArgs = ['customer_transaction_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                        <p class="text-sm text-gray-600 mt-3">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            Please complete your payment using the details above, then enter your payment information here.
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <!-- Cost Breakdown -->
                            <div id="cost-breakdown" class="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl p-6 border border-orange-200">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <i class="fas fa-calculator text-orange-600 mr-2"></i>
                                    Cost Breakdown
                                </h3>
                                <div class="space-y-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Base Cost:</span>
                                        <span class="font-medium" id="base-cost"><?php echo e(\App\Models\Setting::formatCurrency(0)); ?></span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Distance Cost:</span>
                                        <span class="font-medium" id="distance-cost"><?php echo e(\App\Models\Setting::formatCurrency(0)); ?></span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Weight Cost:</span>
                                        <span class="font-medium" id="weight-cost"><?php echo e(\App\Models\Setting::formatCurrency(0)); ?></span>
                                    </div>
                                    <hr class="border-orange-200">
                                    <div class="flex justify-between items-center text-lg font-bold">
                                        <span class="text-gray-900">Total Cost:</span>
                                        <span class="text-orange-600" id="total-cost"><?php echo e(\App\Models\Setting::formatCurrency(25)); ?></span>
                                    </div>
                                </div>
                                <div id="cost-loading" class="hidden text-center py-4">
                                    <div class="inline-flex items-center">
                                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-600 mr-2"></div>
                                        <span class="text-sm text-gray-600">Calculating cost...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 5: Review & Confirm -->
                    <div class="form-step" data-step="5">
                        <div class="step-header mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-2">Review your booking</h2>
                            <p class="text-gray-600">Please review all details before confirming your delivery</p>
                        </div>

                        <div class="space-y-6">
                            <!-- Booking Summary -->
                            <div class="grid md:grid-cols-2 gap-6">
                                <!-- Delivery Details -->
                                <div class="review-section">
                                    <h3 class="review-section-title">
                                        <i class="fas fa-route text-blue-600 mr-2"></i>
                                        Delivery Details
                                    </h3>
                                    <div class="review-content">
                                        <div class="review-item">
                                            <span class="review-label">From:</span>
                                            <span class="review-value" id="review-pickup">--</span>
                                        </div>
                                        <div class="review-item">
                                            <span class="review-label">To:</span>
                                            <span class="review-value" id="review-delivery">--</span>
                                        </div>
                                        <div class="review-item">
                                            <span class="review-label">Distance:</span>
                                            <span class="review-value" id="review-distance">--</span>
                                        </div>
                                        <div class="review-item">
                                            <span class="review-label">Pickup Time:</span>
                                            <span class="review-value" id="review-pickup-time">--</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Package Details -->
                                <div class="review-section">
                                    <h3 class="review-section-title">
                                        <i class="fas fa-box text-purple-600 mr-2"></i>
                                        Package Details
                                    </h3>
                                    <div class="review-content">
                                        <div class="review-item">
                                            <span class="review-label">Type:</span>
                                            <span class="review-value" id="review-package-type">--</span>
                                        </div>
                                        <div class="review-item">
                                            <span class="review-label">Weight:</span>
                                            <span class="review-value" id="review-weight">--</span>
                                        </div>
                                        <div class="review-item">
                                            <span class="review-label">Description:</span>
                                            <span class="review-value" id="review-description">--</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Details -->
                                <div class="review-section">
                                    <h3 class="review-section-title">
                                        <i class="fas fa-users text-green-600 mr-2"></i>
                                        Contact Details
                                    </h3>
                                    <div class="review-content">
                                        <div class="review-item">
                                            <span class="review-label">Pickup Contact:</span>
                                            <span class="review-value" id="review-pickup-contact">--</span>
                                        </div>
                                        <div class="review-item">
                                            <span class="review-label">Receiver:</span>
                                            <span class="review-value" id="review-receiver-contact">--</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Payment Details -->
                                <div class="review-section">
                                    <h3 class="review-section-title">
                                        <i class="fas fa-credit-card text-orange-600 mr-2"></i>
                                        Payment Details
                                    </h3>
                                    <div class="review-content">
                                        <div class="review-item">
                                            <span class="review-label">Method:</span>
                                            <span class="review-value" id="review-payment-method">--</span>
                                        </div>
                                        <div class="review-item">
                                            <span class="review-label">Total Cost:</span>
                                            <span class="review-value font-bold text-orange-600" id="review-total-cost">--</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Terms and Conditions -->
                            <div class="bg-gray-50 rounded-xl p-6">
                                <label class="flex items-start space-x-3">
                                    <input type="checkbox" id="terms-checkbox" class="mt-1 h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded" required>
                                    <span class="text-sm text-gray-700">
                                        I agree to the <a href="#" class="text-orange-600 hover:text-orange-700 font-medium">Terms and Conditions</a>
                                        and <a href="#" class="text-orange-600 hover:text-orange-700 font-medium">Privacy Policy</a>.
                                        I understand that the delivery cost is an estimate and may vary based on actual distance and package details.
                                    </span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="flex justify-between items-center mt-12 pt-8 border-t border-gray-200">
                        <button type="button" id="prev-btn" class="hidden px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300">
                            <i class="fas fa-arrow-left mr-2"></i>Previous
                        </button>
                        
                        <!-- This div is a spacer to push the next/submit buttons to the right -->
                        <div class="flex-grow"></div>

                        <div class="flex space-x-4">
                            <button type="button" id="next-btn" class="px-8 py-3 bg-orange-500 text-white rounded-xl font-medium hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-300">
                                Next<i class="fas fa-arrow-right ml-2"></i>
                            </button>

                            <button type="submit" id="submit-btn" class="hidden px-8 py-3 bg-green-600 text-white rounded-xl font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-300 disabled:opacity-50 disabled:cursor-not-allowed">
                                <span id="submit-text">
                                    <i class="fas fa-check mr-2"></i>Confirm Booking
                                </span>
                                <span id="submit-loading" class="hidden">
                                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Footer with Company Info -->
        <div class="bg-gray-50 px-8 py-6 border-t mt-[-1px] rounded-b-2xl">
             <div class="text-center text-sm text-gray-600">
                 <p class="mb-2">
                     <strong><?php echo e($companyName); ?></strong> - Your trusted courier service
                 </p>
                 <p class="flex items-center justify-center space-x-4">
                     <?php if($companySettings['phone'] ?? false): ?>
                         <span class="flex items-center">
                             <i class="fas fa-phone mr-1"></i>
                             <?php echo e($companySettings['phone']); ?>

                         </span>
                     <?php endif; ?>
                     <?php if($companySettings['email'] ?? false): ?>
                         <span class="flex items-center">
                             <i class="fas fa-envelope mr-1"></i>
                             <?php echo e($companySettings['email']); ?>

                         </span>
                     <?php endif; ?>
                 </p>
                 <p class="text-xs text-gray-500 mt-2">
                     All prices shown in <?php echo e($currencyCode); ?>. Additional charges may apply for special handling.
                 </p>
             </div>
         </div>
    </div>
</div>

<!-- Success Modal -->
<div id="success-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white p-8 rounded-lg shadow-xl text-center mx-4 max-w-md">
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <i class="fas fa-check text-green-500 text-3xl"></i>
        </div>
        <h2 class="text-2xl font-bold mt-4">Booking Successful!</h2>
        <p class="text-gray-600 mt-2">Your delivery has been scheduled.</p>
        <p class="text-sm text-gray-500 mt-1">Booking ID: <span id="booking-id" class="font-mono font-bold"></span></p>
        <div class="mt-6 space-y-2">
            <button id="view-booking-btn" class="w-full bg-orange-500 text-white font-bold py-2 px-8 rounded-lg hover:bg-orange-600">
                View Booking
            </button>
            <button id="close-modal-btn" class="w-full border border-gray-300 text-gray-700 font-bold py-2 px-8 rounded-lg hover:bg-gray-50">
                Close
            </button>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* --- REFINED MULTISTEP FORM STYLES --- */

/* * This is the core logic for step visibility.
 * We hide all steps by default and only show the one with the '.active' class.
 * This avoids conflicts with external animation libraries like GSAP.
*/
.form-step {
    display: none;
}

.form-step.active {
    display: block;
    /* A subtle fade-in animation for a smoother transition */
    animation: fadeIn 0.4s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(15px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* --- PROGRESS BAR STYLES (Unchanged) --- */
.step-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    transition: all 0.3s ease;
}

.step-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #e5e7eb;
    color: #9ca3af;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    border: 3px solid #e5e7eb;
    transition: all 0.3s ease;
}

.step-indicator.active .step-circle {
    background: #f97316;
    color: white;
    border-color: #f97316;
    transform: scale(1.1);
}

.step-indicator.completed .step-circle {
    background: #10b981;
    color: white;
    border-color: #10b981;
}

.step-label {
    margin-top: 8px;
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    transition: all 0.3s ease;
}

.step-indicator.active .step-label {
    color: #f97316;
    font-weight: 600;
}

.step-indicator.completed .step-label {
    color: #10b981;
    font-weight: 600;
}

.step-connector {
    width: 60px;
    height: 2px;
    background: #e5e7eb;
    margin: 0 16px;
    margin-top: -24px; /* Aligned with the center of the circle */
    position: relative;
    z-index: -1;
    transition: background-color 0.3s ease;
}

.step-indicator.completed + .step-connector {
    background: #10b981;
}


/* --- INTERACTIVE ELEMENT STYLES (Unchanged) --- */
/* Package Type Cards */
.package-type-card {
    display: block;
    padding: 20px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    text-align: center;
    cursor: pointer;
    background: white;
    transition: all 0.2s ease-in-out;
}

.package-type-card:hover {
    border-color: #f97316;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.15);
}

.package-type-option input:checked + .package-type-card {
    border-color: #f97316;
    background: #fff7ed;
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.15);
}

.package-icon {
    width: 48px;
    height: 48px;
    background: #f3f4f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    font-size: 20px;
    color: #6b7280;
    transition: all 0.2s ease-in-out;
}

.package-type-option input:checked + .package-type-card .package-icon {
    background: #f97316;
    color: white;
}

.package-title { font-weight: 600; color: #1f2937; margin-bottom: 4px; }
.package-desc { font-size: 12px; color: #6b7280; margin-bottom: 8px; }
.package-size { font-size: 11px; color: #9ca3af; font-weight: 500; }
.package-price { font-size: 12px; color: #f97316; font-weight: 600; margin-top: 4px; }

/* Payment Method Cards */
.payment-method-card {
    display: block;
    padding: 24px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    text-align: center;
    cursor: pointer;
    background: white;
    position: relative;
    transition: all 0.2s ease-in-out;
}

.payment-method-card:hover {
    border-color: #f97316;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.15);
}

.payment-method-option input:checked + .payment-method-card {
    border-color: #f97316;
    background: #fff7ed;
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.15);
}

.payment-icon {
    width: 56px;
    height: 56px;
    background: #f3f4f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 24px;
    color: #6b7280;
    transition: all 0.2s ease-in-out;
}

.payment-method-option input:checked + .payment-method-card .payment-icon {
    background: #f97316;
    color: white;
}

.payment-title { font-weight: 600; color: #1f2937; margin-bottom: 8px; font-size: 16px; }
.payment-desc { font-size: 14px; color: #6b7280; }
.payment-badge { position: absolute; top: -8px; right: -8px; background: #10b981; color: white; font-size: 10px; font-weight: 600; padding: 4px 8px; border-radius: 12px; text-transform: uppercase; }

/* Review Section Styles */
.review-section { background: white; border: 1px solid #e5e7eb; border-radius: 12px; padding: 20px; }
.review-section-title { font-size: 16px; font-weight: 600; color: #1f2937; margin-bottom: 16px; display: flex; align-items: center; }
.review-content { space-y: 12px; }
.review-item { display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px; }
.review-label { font-weight: 500; color: #6b7280; min-width: 100px; }
.review-value { color: #1f2937; text-align: right; flex: 1; margin-left: 16px; }

/* Contact Section Styles */
.contact-section { background: #f9fafb; border-radius: 12px; padding: 24px; border: 1px solid #e5e7eb; }

/* Step Header Styles */
.step-header { text-align: center; margin-bottom: 32px; }

/* Location Input Styles */
.location-input-group { position: relative; }
.pickup-suggestions, .delivery-suggestions { background: white; border: 1px solid #e5e7eb; border-radius: 8px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); max-height: 200px; overflow-y: auto; z-index: 10; }
.suggestion-item { padding: 12px 16px; cursor: pointer; border-bottom: 1px solid #f3f4f6; }
.suggestion-item:hover { background: #f9fafb; }
.suggestion-item:last-child { border-bottom: none; }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- Google Maps JavaScript API -->
<script async defer src="https://maps.googleapis.com/maps/api/js?key=<?php echo e(config('services.google_maps.api_key')); ?>&libraries=places&callback=initGoogleMaps"></script>

<script>
// Global variables for Google Maps
let map;
let pickupAutocomplete;
let deliveryAutocomplete;
let directionsService;
let directionsRenderer;
let pickupMarker;
let deliveryMarker;

// Initialize Google Maps when API is loaded
function initGoogleMaps() {
    console.log('Google Maps API loaded');
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeBookingForm);
    } else {
        initializeBookingForm();
    }
}

function initializeBookingForm() {
    // --- FORM STATE & ELEMENTS ---
    let currentStep = 1;
    const totalSteps = 5;
    const form = document.getElementById('booking-form');
    const nextBtn = document.getElementById('next-btn');
    const prevBtn = document.getElementById('prev-btn');
    const submitBtn = document.getElementById('submit-btn');
    const submitText = document.getElementById('submit-text');
    const submitLoading = document.getElementById('submit-loading');
    const successModal = document.getElementById('success-modal');
    const formSteps = document.querySelectorAll('.form-step');
    const progressIndicators = document.querySelectorAll('.step-indicator');
    const progressConnectors = document.querySelectorAll('.step-connector');

    // Initialize Google Maps components
    initializeGoogleMapsComponents();

    // --- GOOGLE MAPS FUNCTIONS ---

    function initializeGoogleMapsComponents() {
        if (typeof google === 'undefined' || !google.maps) {
            console.warn('Google Maps API not loaded yet');
            return;
        }

        // Initialize map
        const mapElement = document.getElementById('booking-map');
        if (mapElement) {
            map = new google.maps.Map(mapElement, {
                zoom: 12,
                center: { lat: 5.6037, lng: -0.1870 }, // Accra, Ghana
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                styles: [
                    {
                        featureType: 'poi',
                        elementType: 'labels',
                        stylers: [{ visibility: 'off' }]
                    }
                ]
            });

            directionsService = new google.maps.DirectionsService();
            directionsRenderer = new google.maps.DirectionsRenderer({
                suppressMarkers: true,
                polylineOptions: {
                    strokeColor: '#F59E0B',
                    strokeWeight: 4
                }
            });
            directionsRenderer.setMap(map);
        }

        // Initialize autocomplete for pickup location
        const pickupInput = document.getElementById('pickup_address');
        if (pickupInput) {
            pickupAutocomplete = new google.maps.places.Autocomplete(pickupInput, {
                componentRestrictions: { country: 'gh' },
                fields: ['place_id', 'formatted_address', 'geometry', 'name'],
                types: ['address']
            });

            pickupAutocomplete.addListener('place_changed', function() {
                const place = pickupAutocomplete.getPlace();
                if (place.geometry) {
                    updateLocationData('pickup', place);
                    updateMapAndRoute();
                }
            });
        }

        // Initialize autocomplete for delivery location
        const deliveryInput = document.getElementById('delivery_address');
        if (deliveryInput) {
            deliveryAutocomplete = new google.maps.places.Autocomplete(deliveryInput, {
                componentRestrictions: { country: 'gh' },
                fields: ['place_id', 'formatted_address', 'geometry', 'name'],
                types: ['address']
            });

            deliveryAutocomplete.addListener('place_changed', function() {
                const place = deliveryAutocomplete.getPlace();
                if (place.geometry) {
                    updateLocationData('delivery', place);
                    updateMapAndRoute();
                }
            });
        }

        // Setup auto-location buttons
        setupAutoLocationButtons();
    }

    function updateLocationData(type, place) {
        const lat = place.geometry.location.lat();
        const lng = place.geometry.location.lng();

        document.getElementById(`${type}_latitude`).value = lat;
        document.getElementById(`${type}_longitude`).value = lng;
        document.getElementById(`${type}_place_id`).value = place.place_id;
        document.getElementById(`${type}_address`).value = place.formatted_address;
    }

    function setupAutoLocationButtons() {
        // Pickup location button
        document.getElementById('pickup-locate-btn').addEventListener('click', function() {
            getCurrentLocation('pickup');
        });

        // Delivery location button
        document.getElementById('delivery-locate-btn').addEventListener('click', function() {
            getCurrentLocation('delivery');
        });
    }

    function getCurrentLocation(type) {
        const button = document.getElementById(`${type}-locate-btn`);
        const originalContent = button.innerHTML;

        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        if (!navigator.geolocation) {
            alert('Geolocation is not supported by this browser.');
            button.innerHTML = originalContent;
            button.disabled = false;
            return;
        }

        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;

                // Reverse geocode to get address
                const geocoder = new google.maps.Geocoder();
                geocoder.geocode({ location: { lat, lng } }, function(results, status) {
                    if (status === 'OK' && results[0]) {
                        const place = results[0];
                        document.getElementById(`${type}_address`).value = place.formatted_address;
                        document.getElementById(`${type}_latitude`).value = lat;
                        document.getElementById(`${type}_longitude`).value = lng;
                        document.getElementById(`${type}_place_id`).value = place.place_id;

                        updateMapAndRoute();
                    } else {
                        alert('Unable to get address for your location.');
                    }

                    button.innerHTML = originalContent;
                    button.disabled = false;
                });
            },
            function(error) {
                let message = 'Unable to get your location.';
                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        message = 'Location access denied. Please enable location permissions.';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        message = 'Location information unavailable.';
                        break;
                    case error.TIMEOUT:
                        message = 'Location request timed out.';
                        break;
                }
                alert(message);
                button.innerHTML = originalContent;
                button.disabled = false;
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 300000
            }
        );
    }

    function updateMapAndRoute() {
        const pickupLat = parseFloat(document.getElementById('pickup_latitude').value);
        const pickupLng = parseFloat(document.getElementById('pickup_longitude').value);
        const deliveryLat = parseFloat(document.getElementById('delivery_latitude').value);
        const deliveryLng = parseFloat(document.getElementById('delivery_longitude').value);

        if (!map || isNaN(pickupLat) || isNaN(pickupLng) || isNaN(deliveryLat) || isNaN(deliveryLng)) {
            return;
        }

        // Clear existing markers
        if (pickupMarker) pickupMarker.setMap(null);
        if (deliveryMarker) deliveryMarker.setMap(null);

        // Add pickup marker
        pickupMarker = new google.maps.Marker({
            position: { lat: pickupLat, lng: pickupLng },
            map: map,
            title: 'Pickup Location',
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" fill="#10B981" stroke="white" stroke-width="2"/>
                        <path d="M8 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            }
        });

        // Add delivery marker
        deliveryMarker = new google.maps.Marker({
            position: { lat: deliveryLat, lng: deliveryLng },
            map: map,
            title: 'Delivery Location',
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" fill="#EF4444" stroke="white" stroke-width="2"/>
                        <path d="M12 8v4l3 3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            }
        });

        // Calculate and display route
        directionsService.route({
            origin: { lat: pickupLat, lng: pickupLng },
            destination: { lat: deliveryLat, lng: deliveryLng },
            travelMode: google.maps.TravelMode.DRIVING
        }, function(result, status) {
            if (status === 'OK') {
                directionsRenderer.setDirections(result);

                // Update distance and duration
                const route = result.routes[0];
                if (route && route.legs && route.legs.length > 0) {
                    const leg = route.legs[0];
                    document.getElementById('estimated-distance').textContent = leg.distance.text;
                    document.getElementById('estimated-duration').textContent = leg.duration.text;
                    document.getElementById('route-preview').classList.remove('hidden');
                    document.getElementById('map-preview').classList.remove('hidden');
                }

                // Fit map to show route
                const bounds = new google.maps.LatLngBounds();
                bounds.extend({ lat: pickupLat, lng: pickupLng });
                bounds.extend({ lat: deliveryLat, lng: deliveryLng });
                map.fitBounds(bounds);
            }
        });
    }

    // --- CORE FUNCTIONS ---

    /**
     * Displays the specified step and hides all others.
     * @param {number} stepNumber The step to display (1-based).
     */
    const showStep = (stepNumber) => {
        formSteps.forEach(step => {
            step.classList.toggle('active', parseInt(step.dataset.step) === stepNumber);
        });
        updateProgress(stepNumber);
        updateNavigationButtons(stepNumber);
        
        // Trigger necessary actions when a step becomes active
        if (stepNumber === 4) {
            calculateCost();
        }
        if (stepNumber === 5) {
            updateReviewSection();
        }
    };

    /**
     * Updates the visual progress bar at the top of the form.
     * @param {number} currentStep The currently active step.
     */
    const updateProgress = (currentStep) => {
        progressIndicators.forEach((indicator, index) => {
            const step = index + 1;
            indicator.classList.remove('active', 'completed');
            if (step < currentStep) {
                indicator.classList.add('completed');
            } else if (step === currentStep) {
                indicator.classList.add('active');
            }
        });
        progressConnectors.forEach((connector, index) => {
            connector.classList.toggle('completed', (index + 1) < currentStep);
        });
    };

    /**
     * Shows/hides the Previous, Next, and Submit buttons based on the current step.
     * @param {number} currentStep The currently active step.
     */
    const updateNavigationButtons = (currentStep) => {
        prevBtn.classList.toggle('hidden', currentStep === 1);
        nextBtn.classList.toggle('hidden', currentStep === totalSteps);
        submitBtn.classList.toggle('hidden', currentStep !== totalSteps);
    };

    /**
     * Validates all required inputs within the current step.
     * @param {number} stepNumber The step to validate.
     * @returns {boolean} True if the step is valid, false otherwise.
     */
    const validateStep = (stepNumber) => {
        let isValid = true;
        const currentStepElement = document.querySelector(`.form-step[data-step="${stepNumber}"]`);
        
        // Remove previous validation messages
        currentStepElement.querySelectorAll('.validation-error').forEach(el => el.remove());

        const requiredInputs = currentStepElement.querySelectorAll('[required]');

        requiredInputs.forEach(input => {
            input.classList.remove('border-red-500');
            let isInputValid = true;

            // Check radio buttons
            if (input.type === 'radio') {
                const radioGroup = currentStepElement.querySelectorAll(`input[name="${input.name}"]`);
                if (![...radioGroup].some(radio => radio.checked)) {
                    isInputValid = false;
                    // Add error class to the card labels for better UX
                    document.querySelectorAll(`label[for^="${input.name.replace('_', '-')}"]`).forEach(label => {
                        label.closest('.package-type-option, .payment-method-option')?.querySelector('.package-type-card, .payment-method-card').classList.add('border-red-500');
                    });
                }
            } 
            // Check checkboxes
            else if (input.type === 'checkbox') {
                if (!input.checked) {
                    isInputValid = false;
                }
            } 
            // Check other text/select inputs
            else {
                if (!input.value.trim()) {
                    isInputValid = false;
                }
            }

            if (!isInputValid) {
                isValid = false;
                input.classList.add('border-red-500');
            }
        });

        if (!isValid) {
            showValidationError(currentStepElement, 'Please fill in all required fields.');
        }

        return isValid;
    };

    /**
     * Displays a validation error message within a step.
     * @param {HTMLElement} stepElement The step element where the error occurred.
     * @param {string} message The error message to display.
     */
    const showValidationError = (stepElement, message) => {
        const stepHeader = stepElement.querySelector('.step-header');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'validation-error bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg relative mb-6 text-left';
        errorDiv.innerHTML = `<strong class="font-bold"><i class="fas fa-exclamation-triangle mr-2"></i>Error:</strong><span class="block sm:inline ml-2">${message}</span>`;
        stepHeader.after(errorDiv);
        
        // Automatically remove after 5 seconds
        setTimeout(() => errorDiv.remove(), 5000);
    };

    /**
     * Populates the review section (Step 5) with data from the form.
     */
    const updateReviewSection = () => {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // Helper to get text from a selected option
        const getSelectedText = (selectName) => {
            const select = form.querySelector(`select[name="${selectName}"]`);
            return select ? select.options[select.selectedIndex].text : '--';
        };
        
        // Helper to get text from a checked radio's label
        const getRadioLabelText = (radioName, selector) => {
            const radio = form.querySelector(`input[name="${radioName}"]:checked`);
            return radio ? radio.nextElementSibling.querySelector(selector).textContent : '--';
        }

        // Populate fields
        document.getElementById('review-pickup').textContent = data.pickup_address || '--';
        document.getElementById('review-delivery').textContent = data.delivery_address || '--';
        document.getElementById('review-distance').textContent = document.getElementById('estimated-distance').textContent;
        document.getElementById('review-pickup-time').textContent = getSelectedText('pickup_time_preference');
        
        document.getElementById('review-package-type').textContent = getRadioLabelText('package_type', '.package-title');
        document.getElementById('review-weight').textContent = data.package_weight ? `${data.package_weight} kg` : '--';
        document.getElementById('review-description').textContent = data.package_description || 'N/A';
        
        document.getElementById('review-pickup-contact').textContent = `${data.pickup_person_name} (${data.pickup_person_phone})`;
        document.getElementById('review-receiver-contact').textContent = `${data.receiver_name} (${data.receiver_phone})`;
        
        document.getElementById('review-payment-method').textContent = getRadioLabelText('payment_method', '.payment-title');
        document.getElementById('review-total-cost').textContent = document.getElementById('total-cost').textContent;
    };
    
    // --- API & ASYNC OPERATIONS ---
    let costCalculationTimeout;
    /**
     * Fetches the estimated cost from the server.
     */
    const calculateCost = async () => {
        const pickupAddress = document.getElementById('pickup_address').value.trim();
        const deliveryAddress = document.getElementById('delivery_address').value.trim();
        const packageType = form.querySelector('input[name="package_type"]:checked')?.value;
        const packageWeight = document.getElementById('package_weight').value || 0;

        if (!pickupAddress || !deliveryAddress || !packageType) return;

        const costLoadingEl = document.getElementById('cost-loading');
        costLoadingEl.classList.remove('hidden');

        try {
            const response = await fetch('<?php echo e(route("api.bookings.calculate-cost")); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    pickup_address: pickupAddress,
                    delivery_address: deliveryAddress,
                    package_type: packageType,
                    package_weight: packageWeight
                })
            });

            if (!response.ok) throw new Error(`Server error: ${response.statusText}`);
            
            const data = await response.json();

            if (data.success) {
                document.getElementById('base-cost').textContent = data.breakdown.base_cost_formatted;
                document.getElementById('distance-cost').textContent = data.breakdown.distance_cost_formatted;
                document.getElementById('weight-cost').textContent = data.breakdown.weight_cost_formatted;
                document.getElementById('total-cost').textContent = data.formatted_cost;

                document.getElementById('estimated-distance').textContent = data.distance || '-- km';
                document.getElementById('estimated-duration').textContent = data.duration || '-- mins';
                document.getElementById('route-preview').classList.remove('hidden');
            } else {
                console.error('Cost calculation failed:', data.message);
            }
        } catch (error) {
            console.error('An error occurred during cost calculation:', error);
            // Optionally show a user-facing error message
        } finally {
            costLoadingEl.classList.add('hidden');
        }
    };

    // --- EVENT LISTENERS ---
    
    nextBtn.addEventListener('click', () => {
        if (validateStep(currentStep) && currentStep < totalSteps) {
            currentStep++;
            showStep(currentStep);
        }
    });

    prevBtn.addEventListener('click', () => {
        if (currentStep > 1) {
            currentStep--;
            showStep(currentStep);
        }
    });

    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        if (!validateStep(currentStep)) return;

        submitBtn.disabled = true;
        submitText.classList.add('hidden');
        submitLoading.classList.remove('hidden');

        try {
            const response = await fetch(form.action, {
                method: 'POST',
                body: new FormData(form),
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                }
            });
            const data = await response.json();

            if (data.success) {
                document.getElementById('booking-id').textContent = data.booking_id;
                successModal.classList.remove('hidden');
            } else {
                alert(data.message || 'An unknown error occurred. Please try again.');
            }
        } catch (error) {
            console.error('Form submission error:', error);
            alert('A network error occurred. Please check your connection and try again.');
        } finally {
            submitBtn.disabled = false;
            submitText.classList.remove('hidden');
            submitLoading.classList.add('hidden');
        }
    });

    // Add listeners to inputs that trigger cost calculation
    ['pickup_address', 'delivery_address', 'package_weight'].forEach(id => {
        document.getElementById(id).addEventListener('input', () => {
            clearTimeout(costCalculationTimeout);
            costCalculationTimeout = setTimeout(calculateCost, 500); // Debounce
        });
    });

    form.querySelectorAll('input[name="package_type"]').forEach(radio => {
        radio.addEventListener('change', calculateCost);
    });
    
    // Listeners to remove validation errors on interaction
    form.addEventListener('input', (e) => {
        if (e.target.hasAttribute('required')) {
            e.target.classList.remove('border-red-500');
        }
    });
    form.querySelectorAll('input[type="radio"]').forEach(radio => {
        radio.addEventListener('change', (e) => {
            const name = e.target.name;
            document.querySelectorAll(`input[name="${name}"]`).forEach(r => {
                r.closest('.package-type-option, .payment-method-option')?.querySelector('.package-type-card, .payment-method-card').classList.remove('border-red-500');
            });
        });
    });

    // Payment method change handler
    form.querySelectorAll('input[name="payment_method"]').forEach(radio => {
        radio.addEventListener('change', (e) => {
            // Hide all offline payment details
            document.querySelectorAll('.offline-payment-details').forEach(detail => {
                detail.classList.add('hidden');
            });

            // Show details for selected offline payment method
            if (e.target.value.startsWith('offline_method_')) {
                const detailsElement = document.getElementById(`offline-details-${e.target.value}`);
                if (detailsElement) {
                    detailsElement.classList.remove('hidden');

                    // Make payment feedback fields required for offline payments
                    const paymentNameField = detailsElement.querySelector('input[name="customer_payment_name"]');
                    const transactionIdField = detailsElement.querySelector('input[name="customer_transaction_id"]');
                    if (paymentNameField) paymentNameField.setAttribute('required', 'required');
                    if (transactionIdField) transactionIdField.setAttribute('required', 'required');
                }
            } else {
                // Remove required attribute from payment feedback fields for non-offline payments
                document.querySelectorAll('input[name="customer_payment_name"], input[name="customer_transaction_id"]').forEach(field => {
                    field.removeAttribute('required');
                });
            }
        });
    });

    // Modal close listeners
    document.getElementById('close-modal-btn').addEventListener('click', () => {
        window.location.href = '<?php echo e(route("home")); ?>'; // Or redirect to dashboard
    });
    document.getElementById('view-booking-btn').addEventListener('click', () => {
        window.location.href = '<?php echo e(route("customer.dashboard")); ?>';
    });


    // Map toggle functionality
    document.getElementById('toggle-map').addEventListener('click', function() {
        const mapContainer = document.getElementById('booking-map');
        const button = this;

        if (mapContainer.style.height === '200px') {
            mapContainer.style.height = '400px';
            button.innerHTML = '<i class="fas fa-compress-arrows-alt mr-1"></i>Collapse';
        } else {
            mapContainer.style.height = '200px';
            button.innerHTML = '<i class="fas fa-expand-arrows-alt mr-1"></i>Expand';
        }

        // Trigger map resize
        setTimeout(() => {
            if (map) {
                google.maps.event.trigger(map, 'resize');
                if (pickupMarker && deliveryMarker) {
                    const bounds = new google.maps.LatLngBounds();
                    bounds.extend(pickupMarker.getPosition());
                    bounds.extend(deliveryMarker.getPosition());
                    map.fitBounds(bounds);
                }
            }
        }, 300);
    });

    // --- INITIALIZATION ---
    showStep(currentStep); // Initialize the form to show the first step

    // Pre-calculate cost if addresses are already filled (e.g., from query params)
    if (document.getElementById('pickup_address').value && document.getElementById('delivery_address').value) {
        calculateCost();
    }
}

// Fallback initialization if Google Maps fails to load
document.addEventListener('DOMContentLoaded', function() {
    // Check if Google Maps loaded, if not initialize basic form functionality
    setTimeout(() => {
        if (typeof google === 'undefined' || !google.maps) {
            console.warn('Google Maps API failed to load, initializing basic form');
            initializeBookingForm();
        }
    }, 5000);
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/booking/create.blade.php ENDPATH**/ ?>